import cv2
import numpy as np
from hand_detection_app import HandDetector

def test_with_webcam():
    """Test hand detection with webcam"""
    print("Testing with webcam...")
    detector = HandDetector()
    
    cap = cv2.VideoCapture(0)
    if not cap.isOpened():
        print("Error: Could not open webcam")
        return
    
    print("Press 'q' to quit webcam test")
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
            
        frame = cv2.flip(frame, 1)
        processed_frame, hand_data = detector.detect_hands(frame)
        
        # Print hand information
        for i, hand in enumerate(hand_data):
            print(f"Hand {i+1}: {hand['label']} (Confidence: {hand['confidence']:.2f})")
            x_min, y_min, x_max, y_max = hand['bounding_box']
            print(f"  Bounding Box: ({x_min}, {y_min}) to ({x_max}, {y_max})")
        
        cv2.imshow('Hand Detection Test', processed_frame)
        
        if cv2.waitKey(1) & 0xFF == ord('q'):
            break
    
    cap.release()
    cv2.destroyAllWindows()

def test_with_image(image_path):
    """Test hand detection with a static image"""
    print(f"Testing with image: {image_path}")
    
    detector = HandDetector()
    
    # Load image
    image = cv2.imread(image_path)
    if image is None:
        print(f"Error: Could not load image {image_path}")
        return
    
    # Detect hands
    processed_image, hand_data = detector.detect_hands(image)
    
    # Print results
    print(f"Number of hands detected: {len(hand_data)}")
    for i, hand in enumerate(hand_data):
        print(f"Hand {i+1}:")
        print(f"  Label: {hand['label']}")
        print(f"  Confidence: {hand['confidence']:.2f}")
        x_min, y_min, x_max, y_max = hand['bounding_box']
        print(f"  Bounding Box: ({x_min}, {y_min}) to ({x_max}, {y_max})")
        print(f"  Box Size: {x_max - x_min} x {y_max - y_min}")
    
    # Display result
    cv2.imshow('Hand Detection Result', processed_image)
    cv2.waitKey(0)
    cv2.destroyAllWindows()
    
    # Save result
    output_path = f"detected_{image_path}"
    cv2.imwrite(output_path, processed_image)
    print(f"Result saved as: {output_path}")

def create_test_image():
    """Create a simple test image with hand gesture"""
    print("Creating a test image...")
    
    # Create a blank image
    img = np.ones((480, 640, 3), dtype=np.uint8) * 255
    
    # Add some text
    cv2.putText(img, "Place your hand in front of the camera", (50, 240), 
                cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
    cv2.putText(img, "and run the hand detection app", (100, 280), 
                cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
    
    # Save the test image
    cv2.imwrite("test_image.jpg", img)
    print("Test image created: test_image.jpg")

def main():
    """Main test function"""
    print("Hand Detection Test Suite")
    print("=" * 30)
    
    while True:
        print("\nChoose an option:")
        print("1. Test with webcam")
        print("2. Test with image file")
        print("3. Create test image")
        print("4. Exit")
        
        choice = input("Enter your choice (1-4): ").strip()
        
        if choice == '1':
            test_with_webcam()
        elif choice == '2':
            image_path = input("Enter image path: ").strip()
            test_with_image(image_path)
        elif choice == '3':
            create_test_image()
        elif choice == '4':
            print("Exiting...")
            break
        else:
            print("Invalid choice. Please try again.")

if __name__ == "__main__":
    main()
