import cv2
import mediapipe as mp
import numpy as np

class HandDetector:
    def __init__(self, max_num_hands=2, min_detection_confidence=0.5, min_tracking_confidence=0.5):
        """
        Initialize the hand detector with MediaPipe
        
        Args:
            max_num_hands: Maximum number of hands to detect
            min_detection_confidence: Minimum confidence for hand detection
            min_tracking_confidence: Minimum confidence for hand tracking
        """
        self.mp_hands = mp.solutions.hands
        self.hands = self.mp_hands.Hands(
            static_image_mode=False,
            max_num_hands=max_num_hands,
            min_detection_confidence=min_detection_confidence,
            min_tracking_confidence=min_tracking_confidence
        )
        self.mp_drawing = mp.solutions.drawing_utils
        
    def get_bounding_box(self, landmarks, image_width, image_height):
        """
        Calculate bounding box coordinates from hand landmarks
        
        Args:
            landmarks: Hand landmarks from MediaPipe
            image_width: Width of the image
            image_height: Height of the image
            
        Returns:
            tuple: (x_min, y_min, x_max, y_max) bounding box coordinates
        """
        x_coords = [landmark.x * image_width for landmark in landmarks.landmark]
        y_coords = [landmark.y * image_height for landmark in landmarks.landmark]
        
        x_min = int(min(x_coords))
        x_max = int(max(x_coords))
        y_min = int(min(y_coords))
        y_max = int(max(y_coords))
        
        return x_min, y_min, x_max, y_max
    
    def detect_hands(self, image):
        """
        Detect hands in the given image and return bounding boxes
        
        Args:
            image: Input image (BGR format)
            
        Returns:
            tuple: (processed_image, hand_data_list)
                processed_image: Image with bounding boxes drawn
                hand_data_list: List of dictionaries containing hand information
        """
        # Convert BGR to RGB for MediaPipe
        rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        results = self.hands.process(rgb_image)
        
        # Create a copy of the original image for drawing
        output_image = image.copy()
        hand_data_list = []
        
        if results.multi_hand_landmarks:
            for idx, hand_landmarks in enumerate(results.multi_hand_landmarks):
                # Get image dimensions
                height, width, _ = image.shape
                
                # Calculate bounding box
                x_min, y_min, x_max, y_max = self.get_bounding_box(hand_landmarks, width, height)
                
                # Add padding to bounding box
                padding = 20
                x_min = max(0, x_min - padding)
                y_min = max(0, y_min - padding)
                x_max = min(width, x_max + padding)
                y_max = min(height, y_max + padding)
                
                # Get hand label (Left/Right)
                hand_label = results.multi_handedness[idx].classification[0].label
                confidence = results.multi_handedness[idx].classification[0].score
                
                # Store hand data
                hand_data = {
                    'label': hand_label,
                    'confidence': confidence,
                    'bounding_box': (x_min, y_min, x_max, y_max),
                    'landmarks': hand_landmarks
                }
                hand_data_list.append(hand_data)
                
                # Draw bounding box
                cv2.rectangle(output_image, (x_min, y_min), (x_max, y_max), (0, 255, 0), 2)
                
                # Draw label and confidence
                label_text = f"{hand_label}: {confidence:.2f}"
                cv2.putText(output_image, label_text, (x_min, y_min - 10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)

                # Draw hand landmarks
                self.mp_drawing.draw_landmarks(
                    output_image, hand_landmarks, self.mp_hands.HAND_CONNECTIONS)
        
        return output_image, hand_data_list

def main():
    """
    Main function to run the hand detection app
    """
    # Initialize hand detector
    detector = HandDetector(max_num_hands=2, min_detection_confidence=0.7)
    
    # Initialize webcam
    cap = cv2.VideoCapture(0)
    
    if not cap.isOpened():
        print("Error: Could not open webcam")
        return
    
    print("Hand Detection App Started!")
    print("Press 'q' to quit")
    print("Press 's' to save current frame")
    
    frame_count = 0
    
    while True:
        ret, frame = cap.read()
        if not ret:
            print("Error: Could not read frame")
            break
        
        # Flip frame horizontally for mirror effect
        frame = cv2.flip(frame, 1)
        
        # Detect hands
        processed_frame, hand_data = detector.detect_hands(frame)
        
        # Display information
        info_text = f"Hands detected: {len(hand_data)}"
        cv2.putText(processed_frame, info_text, (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        
        # Display the frame
        cv2.imshow('Hand Detection', processed_frame)
        
        # Handle key presses
        key = cv2.waitKey(1) & 0xFF
        if key == ord('q'):
            break
        elif key == ord('s'):
            # Save current frame
            filename = f"hand_detection_frame_{frame_count}.jpg"
            cv2.imwrite(filename, processed_frame)
            print(f"Frame saved as {filename}")
            frame_count += 1
    
    # Clean up
    cap.release()
    cv2.destroyAllWindows()
    print("Hand Detection App Closed!")

if __name__ == "__main__":
    main()
