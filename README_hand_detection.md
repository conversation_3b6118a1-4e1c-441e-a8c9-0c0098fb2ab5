# Hand Detection App with MediaPipe

This application uses Google's MediaPipe library to detect hands in real-time video streams and static images, drawing bounding boxes around detected hands.

## Features

- **Real-time hand detection** using webcam
- **Bounding box visualization** around detected hands
- **Hand classification** (Left/Right hand detection)
- **Confidence scores** for each detection
- **Static image processing** support
- **Frame saving** functionality
- **Hand landmarks** visualization (optional)

## Files

- `hand_detection_app.py` - Main hand detection application
- `test_hand_detection.py` - Test suite for the hand detector
- `requirements.txt` - Required Python packages

## Installation

1. Make sure you have Python 3.7+ installed
2. Install the required packages:
   ```bash
   pip install -r requirements.txt
   ```

## Usage

### Running the Main App

```bash
python hand_detection_app.py
```

**Controls:**
- Press `q` to quit the application
- Press `s` to save the current frame with detections

### Running Tests

```bash
python test_hand_detection.py
```

The test suite offers several options:
1. Test with webcam (real-time detection)
2. Test with image file (static image processing)
3. Create a test image
4. Exit

### Using the HandDetector Class

```python
from hand_detection_app import HandDetector
import cv2

# Initialize detector
detector = HandDetector(max_num_hands=2, min_detection_confidence=0.7)

# Load an image
image = cv2.imread('your_image.jpg')

# Detect hands
processed_image, hand_data = detector.detect_hands(image)

# Access hand information
for hand in hand_data:
    print(f"Hand: {hand['label']}")
    print(f"Confidence: {hand['confidence']}")
    print(f"Bounding Box: {hand['bounding_box']}")
```

## Configuration

You can customize the hand detector by adjusting these parameters:

- `max_num_hands`: Maximum number of hands to detect (default: 2)
- `min_detection_confidence`: Minimum confidence for detection (default: 0.5)
- `min_tracking_confidence`: Minimum confidence for tracking (default: 0.5)

## Output Format

The `detect_hands()` method returns:
- **Processed image**: Original image with bounding boxes and labels drawn
- **Hand data list**: List of dictionaries containing:
  - `label`: "Left" or "Right"
  - `confidence`: Detection confidence score (0.0 to 1.0)
  - `bounding_box`: Tuple of (x_min, y_min, x_max, y_max)
  - `landmarks`: MediaPipe hand landmarks object

## Troubleshooting

### Common Issues

1. **Webcam not working:**
   - Check if your webcam is connected and not being used by another application
   - Try changing the camera index in `cv2.VideoCapture(0)` to `cv2.VideoCapture(1)` or higher

2. **Poor detection accuracy:**
   - Ensure good lighting conditions
   - Keep hands clearly visible and unobstructed
   - Adjust `min_detection_confidence` parameter

3. **Performance issues:**
   - Reduce the number of `max_num_hands`
   - Lower the camera resolution if needed

### Requirements

- Python 3.7+
- OpenCV (cv2)
- MediaPipe
- NumPy
- Webcam (for real-time detection)

## Example Output

When hands are detected, you'll see:
- Green bounding boxes around each hand
- Labels showing "Left" or "Right" with confidence scores
- Optional hand landmarks (21 points per hand)
- Real-time hand count display

## Performance Tips

- Use good lighting for better detection accuracy
- Keep hands within the camera frame
- Avoid cluttered backgrounds for better performance
- Adjust confidence thresholds based on your use case

## Next Steps

This basic hand detection app can be extended for:
- Gesture recognition
- Sign language detection
- Hand tracking applications
- Augmented reality projects
- Interactive interfaces
