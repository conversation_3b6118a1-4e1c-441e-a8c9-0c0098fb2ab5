import cv2
import numpy as np
import mediapipe as mp
from tensorflow.keras.models import load_model
import time

# Load the trained model
model = load_model('sign_language_model.h5')

# MediaPipe Hands setup
mp_hands = mp.solutions.hands
hands = mp_hands.Hands(static_image_mode=False,
                       max_num_hands=1,
                       min_detection_confidence=0.5,
                       min_tracking_confidence=0.5)
mp_drawing = mp.solutions.drawing_utils

# Class names for mapping prediction indices to letters
# The labels in the dataset are 0-24, excluding 9 (J) and 25 (Z is not in the dataset)
# The LabelBinarizer in training maps these to 0-23.
class_names = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y']

def get_best_available_prediction(prediction_array, class_names, blocked_letters, contextual_blocked_letters=None):
    """
    Get the best prediction that isn't in the blocked letters list.
    Returns the letter and confidence of the highest confidence available prediction.
    """
    # Combine current blocked letters with contextual blocked letters
    all_blocked_letters = set(blocked_letters)
    if contextual_blocked_letters:
        all_blocked_letters.update(contextual_blocked_letters)

    # Create list of (confidence, letter) pairs
    predictions_with_letters = [(prediction_array[i], class_names[i]) for i in range(len(prediction_array))]

    # Sort by confidence (highest first)
    predictions_with_letters.sort(key=lambda x: x[0], reverse=True)

    # Find the first prediction that isn't blocked
    for confidence, letter in predictions_with_letters:
        if letter not in all_blocked_letters:
            return letter, confidence

    # If all letters are blocked (shouldn't happen), return the best one anyway
    return predictions_with_letters[0][1], predictions_with_letters[0][0]

class DeletionMemory:
    """Manages memory of deleted letters with their word context"""
    def __init__(self):
        # Dictionary: context -> set of deleted letters
        # context is the word prefix before the deleted letter
        self.deletion_memory = {}
        # Track recent deletions for current session
        self.recent_deletions = []
        self.max_recent_deletions = 10

    def add_deletion(self, word_context, deleted_letter):
        """Record a deletion with its word context"""
        if word_context not in self.deletion_memory:
            self.deletion_memory[word_context] = set()

        self.deletion_memory[word_context].add(deleted_letter)

        # Add to recent deletions
        deletion_entry = (word_context, deleted_letter)
        if deletion_entry not in self.recent_deletions:
            self.recent_deletions.append(deletion_entry)
            if len(self.recent_deletions) > self.max_recent_deletions:
                self.recent_deletions.pop(0)

        print(f"🧠 Memory: Recorded deletion of '{deleted_letter}' after '{word_context}'")

    def get_contextual_blocked_letters(self, current_context):
        """Get letters that should be blocked based on context memory"""
        blocked = set()

        # Check exact context match
        if current_context in self.deletion_memory:
            blocked.update(self.deletion_memory[current_context])

        # Check partial context matches (last 1-3 characters)
        for i in range(1, min(4, len(current_context) + 1)):
            partial_context = current_context[-i:]
            if partial_context in self.deletion_memory:
                blocked.update(self.deletion_memory[partial_context])

        return blocked

    def get_memory_summary(self):
        """Get a summary of current deletion memory"""
        total_contexts = len(self.deletion_memory)
        total_deletions = sum(len(letters) for letters in self.deletion_memory.values())
        return f"Memory: {total_contexts} contexts, {total_deletions} deletion patterns"

class WordBuilder:
    def __init__(self, all_class_names):
        self.current_word = ""
        self.last_prediction = ""
        self.prediction_start_time = 0
        self.hold_time_required = 2.0  # Hold prediction for 3 seconds to add letter
        self.confidence_threshold = 0.0  # Minimum confidence to consider prediction (lowered for better detection)
        self.is_holding_prediction = False
        self.all_class_names = all_class_names  # Original full list of letters
        self.blocked_letters = set()  # Letters removed by backspace for current word
        self.deletion_memory = DeletionMemory()  # New: Memory system for deleted letters

    def add_letter(self, letter):
        """Add a letter to the current word and clear blocked letters"""
        self.current_word += letter
        # Clear blocked letters when successfully adding any letter
        if self.blocked_letters:
            print(f"Letter '{letter}' added. Cleared blocked letters: {sorted(list(self.blocked_letters))}")
            self.blocked_letters.clear()
        else:
            print(f"Letter '{letter}' added to word.")
        print(f"Current word: '{self.current_word}'")

    def delete_last_letter(self):
        """Remove the last letter from the current word and block it from predictions"""
        if self.current_word:
            removed_letter = self.current_word[-1]
            word_context = self.current_word[:-1]  # Context before the deleted letter
            self.current_word = self.current_word[:-1]

            # Block this letter from being predicted again for current position
            if removed_letter != ' ':  # Don't block spaces
                self.blocked_letters.add(removed_letter)

                # Add to deletion memory with context
                self.deletion_memory.add_deletion(word_context, removed_letter)

                print(f"❌ Removed '{removed_letter}' and blocked it for this position")
                print(f"🚫 Currently blocked: {', '.join(sorted(list(self.blocked_letters)))}")
                print(f"📝 Current word: '{self.current_word}'")
            else:
                print(f"Removed space. Current word: '{self.current_word}'")
            return True
        return False

    def add_space(self):
        """Add a space to the current word"""
        if self.current_word and not self.current_word.endswith(' '):
            self.current_word += ' '
            print(f"Space added. Current word: '{self.current_word}'")

    def reset_word(self):
        """Reset the word library (clear current word and move to next word)"""
        if self.current_word:
            print(f"Word completed: '{self.current_word.strip()}'")
            completed_word = self.current_word.strip()
            self.current_word = ""
            self.blocked_letters.clear()  # Clear blocked letters for new word
            self.reset_prediction_state()
            print("Starting new word - all letters available")
            return completed_word
        else:
            self.blocked_letters.clear()  # Clear blocked letters even if no word
            print("Starting fresh - all letters available")
        return ""

    def reset_prediction_state(self):
        """Reset prediction tracking state"""
        self.last_prediction = ""
        self.prediction_start_time = 0
        self.is_holding_prediction = False

    def get_word(self):
        """Get the current word"""
        return self.current_word

    def get_hold_progress(self):
        """Get the current hold progress as a percentage (0-100)"""
        if not self.is_holding_prediction:
            return 0
        current_time = time.time()
        elapsed_time = current_time - self.prediction_start_time
        progress = min(100, (elapsed_time / self.hold_time_required) * 100)
        return progress

    def is_letter_blocked(self, letter):
        """Check if a letter is currently blocked from predictions"""
        return letter in self.blocked_letters

    def get_available_letters(self):
        """Get list of letters that can currently be predicted"""
        # Get contextual blocked letters based on current word
        contextual_blocked = self.deletion_memory.get_contextual_blocked_letters(self.current_word)
        all_blocked = self.blocked_letters.union(contextual_blocked)
        return [letter for letter in self.all_class_names if letter not in all_blocked]

    def get_contextual_blocked_letters(self):
        """Get letters blocked based on deletion memory context"""
        return self.deletion_memory.get_contextual_blocked_letters(self.current_word)

    def process_prediction(self, predicted_letter, confidence):
        """Process a new prediction and determine if letter should be added"""
        current_time = time.time()

        # Check if prediction meets confidence threshold
        if confidence < self.confidence_threshold:
            self.reset_prediction_state()
            return False

        # Note: Blocked letter filtering now happens at prediction level,
        # so we don't need to check here anymore

        # Check if this is the same prediction as before
        if predicted_letter == self.last_prediction:
            # Continue holding the same prediction
            if not self.is_holding_prediction:
                self.prediction_start_time = current_time
                self.is_holding_prediction = True

            # Check if we've held long enough
            elapsed_time = current_time - self.prediction_start_time
            if elapsed_time >= self.hold_time_required:
                self.add_letter(predicted_letter)
                self.reset_prediction_state()  # Reset to avoid immediate re-addition
                return True
        else:
            # New prediction detected, start tracking it
            self.last_prediction = predicted_letter
            self.prediction_start_time = current_time
            self.is_holding_prediction = True

        return False


def main():
    """Main function to run the prediction with hand tracking and word building."""
    cap = cv2.VideoCapture(0)
    word_builder = WordBuilder(class_names)

    if not cap.isOpened():
        print("Error: Could not open camera.")
        return

    print("Sign Language Word Builder with Smart Memory")
    print("Controls:")
    print("- Hold a sign steady for 3+ seconds to add letter")
    print("- Press 'backspace' to remove incorrect letter (learns from deletions)")
    print("- Press 's' to add space")
    print("- Press 'n' to finish current word and start next word")
    print("- Press 'enter' to save completed word to file")
    print("- Press 'q' to quit")
    print("\nSmart Features:")
    print("- Memory learns from your deletions and blocks similar patterns")
    print("- Context-aware blocking based on word prefixes")
    print("- Improved predictions based on your correction patterns")

    while True:
        ret, frame = cap.read()
        if not ret:
            print("Error: Failed to capture frame.")
            break

        # Flip the frame horizontally for a selfie-view display
        frame = cv2.flip(frame, 1)

        # Convert the BGR image to RGB
        rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

        # Process the frame and find hands
        results = hands.process(rgb_frame)

        predicted_class = ""
        confidence = 0.0

        if results.multi_hand_landmarks:
            for hand_landmarks in results.multi_hand_landmarks:
                # Draw hand landmarks (optional)
                mp_drawing.draw_landmarks(frame, hand_landmarks, mp_hands.HAND_CONNECTIONS)

                # Get bounding box around the hand
                x_coords = [landmark.x for landmark in hand_landmarks.landmark]
                y_coords = [landmark.y for landmark in hand_landmarks.landmark]

                x_min = int(min(x_coords) * frame.shape[1] - 20)
                y_min = int(min(y_coords) * frame.shape[0] - 20)
                x_max = int(max(x_coords) * frame.shape[1] + 20)
                y_max = int(max(y_coords) * frame.shape[0] + 20)

                # Ensure the bounding box is within the frame
                x_min = max(0, x_min)
                y_min = max(0, y_min)
                x_max = min(frame.shape[1], x_max)
                y_max = min(frame.shape[0], y_max)

                # Draw the bounding box
                cv2.rectangle(frame, (x_min, y_min), (x_max, y_max), (0, 255, 0), 2)

                # Extract the ROI
                roi = frame[y_min:y_max, x_min:x_max]

                if roi.size > 0:
                    # Preprocess the ROI for prediction
                    gray = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)
                    gray = cv2.resize(gray, (28, 28), interpolation=cv2.INTER_AREA)
                    normalized = gray / 255.0
                    prediction_image = normalized.reshape(1, 28, 28, 1)

                    # Make a prediction
                    prediction = model.predict(prediction_image, verbose=0)

                    # Filter out blocked letters (both current and contextual) and get best available prediction
                    contextual_blocked = word_builder.get_contextual_blocked_letters()
                    predicted_class, confidence = get_best_available_prediction(
                        prediction[0], class_names, word_builder.blocked_letters, contextual_blocked
                    )

        # Process prediction for word building
        if predicted_class and confidence > 0:
            word_builder.process_prediction(predicted_class, confidence)

        # Handle keyboard input
        key = cv2.waitKey(1) & 0xFF
        if key == ord('q'):
            break
        elif key == ord('s'):  # Add space
            word_builder.add_space()
        elif key == 8:  # Backspace - remove incorrect letter
            word_builder.delete_last_letter()
        elif key == ord('n'):  # Next word - reset library
            completed_word = word_builder.reset_word()
            if completed_word:
                print(f"Moving to next word. Previous word: '{completed_word}'")
            else:
                print("Starting new word...")
        elif key == 13:  # Enter - save completed word to file
            current_word = word_builder.get_word()
            if current_word.strip():
                with open('created_words.txt', 'a', encoding='utf-8') as f:
                    f.write(current_word.strip() + '\n')
                print(f"Saved word to file: '{current_word.strip()}'")
                word_builder.reset_word()  # Reset after saving

        # Display information on frame
        current_word = word_builder.get_word()

        # Display current prediction (after filtering blocked letters)
        if predicted_class:
            pred_text = f"Predicting: {predicted_class} ({confidence:.2f})"
            cv2.putText(frame, pred_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)

        # Display hold progress (3-second timer) - show even if no progress yet
        hold_progress = word_builder.get_hold_progress()
        if predicted_class and confidence > 0:  # Show timer info when we have a prediction
            if hold_progress > 0:
                progress_text = f"Hold Progress: {hold_progress:.1f}% (3s required)"
                color = (0, 255, 255) if hold_progress < 100 else (0, 255, 0)  # Yellow while holding, green when ready
            else:
                progress_text = f"Hold '{predicted_class}' for 3 seconds to add"
                color = (255, 255, 255)  # White when ready to start

            cv2.putText(frame, progress_text, (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)

            # Draw progress bar
            bar_width = 200
            bar_height = 10
            bar_x = 10
            bar_y = 70
            cv2.rectangle(frame, (bar_x, bar_y), (bar_x + bar_width, bar_y + bar_height), (100, 100, 100), -1)
            if hold_progress > 0:
                progress_width = int((hold_progress / 100) * bar_width)
                cv2.rectangle(frame, (bar_x, bar_y), (bar_x + progress_width, bar_y + bar_height), color, -1)

        # Display current word being built
        word_text = f"Word: {current_word}"
        cv2.putText(frame, word_text, (10, 100), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)

        # Display blocked letters for current letter position (if any)
        contextual_blocked = word_builder.get_contextual_blocked_letters()
        all_blocked = word_builder.blocked_letters.union(contextual_blocked)

        if all_blocked:
            # Show current position blocks
            if word_builder.blocked_letters:
                current_blocked_text = f"Blocked (current): {', '.join(sorted(word_builder.blocked_letters))}"
                cv2.putText(frame, current_blocked_text, (10, 130), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 2)

            # Show contextual blocks
            if contextual_blocked:
                contextual_blocked_text = f"Blocked (memory): {', '.join(sorted(contextual_blocked))}"
                y_pos = 150 if word_builder.blocked_letters else 130
                cv2.putText(frame, contextual_blocked_text, (10, y_pos), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 255), 2)
        else:
            cv2.putText(frame, "All letters available", (10, 130), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)

        # Display available letters count
        available_count = len(word_builder.get_available_letters())
        total_count = len(word_builder.all_class_names)
        available_text = f"Available letters: {available_count}/{total_count}"
        y_pos = 170 if contextual_blocked and word_builder.blocked_letters else 160
        cv2.putText(frame, available_text, (10, y_pos), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)

        # Display memory summary
        memory_summary = word_builder.deletion_memory.get_memory_summary()
        cv2.putText(frame, memory_summary, (10, y_pos + 20), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

        # Display controls
        cv2.putText(frame, "s=space | backspace=delete&block | n=next word | enter=save | q=quit",
                    (10, frame.shape[0] - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

        # Display the resulting frame
        cv2.imshow('Sign Language Word Builder', frame)

    # When everything is done, release the capture
    cap.release()
    cv2.destroyAllWindows()

    # Save final word if any
    final_word = word_builder.get_word()
    if final_word.strip():
        print(f"Final word in progress: '{final_word.strip()}'")
        with open('created_words.txt', 'a', encoding='utf-8') as f:
            f.write(final_word.strip() + '\n')
        print("Final word saved to created_words.txt")


if __name__ == "__main__":
    main() 