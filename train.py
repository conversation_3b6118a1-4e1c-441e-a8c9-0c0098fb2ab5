import argparse
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import seaborn as sns
from tensorflow.keras.callbacks import ReduceLROnPlateau
from tensorflow.keras.layers import (BatchNormalization, Conv2D, Dense, Dropout, Flatten,
                          MaxPool2D)
from tensorflow.keras.models import Sequential
from tensorflow.keras.preprocessing.image import ImageDataGenerator
from sklearn.metrics import classification_report, confusion_matrix
from sklearn.preprocessing import LabelBinarizer


def load_data(train_path, test_path):
    """Loads the Sign Language MNIST dataset."""
    train_df = pd.read_csv(train_path)
    test_df = pd.read_csv(test_path)

    y_train = train_df['label']
    y_test = test_df['label']
    del train_df['label']
    del test_df['label']

    return train_df, test_df, y_train, y_test


def preprocess_data(x_train_df, x_test_df, y_train, y_test):
    """Preprocesses the data."""
    label_binarizer = LabelBinarizer()
    y_train = label_binarizer.fit_transform(y_train)
    y_test = label_binarizer.transform(y_test)

    x_train = x_train_df.values
    x_test = x_test_df.values

    # Normalize the data
    x_train = x_train / 255
    x_test = x_test / 255

    # Reshape data for CNN
    x_train = x_train.reshape(-1, 28, 28, 1)
    x_test = x_test.reshape(-1, 28, 28, 1)

    return x_train, x_test, y_train, y_test, label_binarizer


def create_model():
    """Creates the CNN model."""
    model = Sequential()
    model.add(Conv2D(75, (3, 3), strides=1, padding='same',
              activation='relu', input_shape=(28, 28, 1)))
    model.add(BatchNormalization())
    model.add(MaxPool2D((2, 2), strides=2, padding='same'))
    model.add(Conv2D(50, (3, 3), strides=1, padding='same', activation='relu'))
    model.add(Dropout(0.2))
    model.add(BatchNormalization())
    model.add(MaxPool2D((2, 2), strides=2, padding='same'))
    model.add(Conv2D(25, (3, 3), strides=1, padding='same', activation='relu'))
    model.add(BatchNormalization())
    model.add(MaxPool2D((2, 2), strides=2, padding='same'))
    model.add(Flatten())
    model.add(Dense(units=512, activation='relu'))
    model.add(Dropout(0.3))
    model.add(Dense(units=24, activation='softmax'))
    model.compile(optimizer='adam',
                  loss='categorical_crossentropy', metrics=['accuracy'])
    return model


def main(train_path, test_path, epochs):
    """Main function to train and evaluate the model."""
    # Load and preprocess data
    train_df, test_df, y_train_labels, y_test_labels = load_data(
        train_path, test_path)
    # for classification report
    test_labels_for_report = y_test_labels.copy()
    x_train, x_test, y_train, y_test, label_binarizer = preprocess_data(
        train_df, test_df, y_train_labels, y_test_labels)

    # Data Augmentation
    datagen = ImageDataGenerator(
        rotation_range=10,
        zoom_range=0.1,
        width_shift_range=0.1,
        height_shift_range=0.1,
    )
    datagen.fit(x_train)

    # Learning Rate Reduction
    learning_rate_reduction = ReduceLROnPlateau(
        monitor='val_accuracy', patience=2, verbose=1, factor=0.5, min_lr=0.00001)

    # Create and train the model
    model = create_model()
    model.summary()
    history = model.fit(datagen.flow(x_train, y_train, batch_size=128),
                        epochs=epochs,
                        validation_data=(x_test, y_test),
                        callbacks=[learning_rate_reduction])

    # Evaluate the model
    print("Accuracy of the model is - ", model.evaluate(
        x_test, y_test)[1]*100, "%")

    # Plotting accuracy and loss
    epochs_range = range(epochs)
    fig, ax = plt.subplots(1, 2, figsize=(16, 9))
    ax[0].plot(epochs_range, history.history['accuracy'],
               'go-', label='Training Accuracy')
    ax[0].plot(epochs_range, history.history['val_accuracy'],
               'ro-', label='Validation Accuracy')
    ax[0].set_title('Training & Validation Accuracy')
    ax[0].legend()
    ax[0].set_xlabel("Epochs")
    ax[0].set_ylabel("Accuracy")

    ax[1].plot(epochs_range, history.history['loss'],
               'g-o', label='Training Loss')
    ax[1].plot(epochs_range, history.history['val_loss'],
               'r-o', label='Validation Loss')
    ax[1].set_title('Training & Validation Loss')
    ax[1].legend()
    ax[1].set_xlabel("Epochs")
    ax[1].set_ylabel("Loss")
    plt.savefig('training_history.png')
    plt.show()

    # Classification Report and Confusion Matrix
    prediction_indices = np.argmax(model.predict(x_test), axis=-1)
    predicted_labels = label_binarizer.classes_[prediction_indices]

    # The notebook had a bug where it modified the predictions array for classes > 9
    # This is not needed if we use the label binarizer correctly.
    
    classes = [str(c) for c in label_binarizer.classes_]
    print(classification_report(
        test_labels_for_report, predicted_labels, target_names=classes))

    cm = confusion_matrix(test_labels_for_report, predicted_labels)
    cm_df = pd.DataFrame(cm, index=classes, columns=classes)

    plt.figure(figsize=(15, 15))
    sns.heatmap(cm_df, cmap="Blues", linecolor='black',
                linewidth=1, annot=True, fmt='')
    plt.title("Confusion Matrix")
    plt.savefig('confusion_matrix.png')
    plt.show()

    # Save the model
    model.save('sign_language_model.h5')
    print("Model saved as sign_language_model.h5")


if __name__ == "__main__":
    train_path = "/kaggle/input/sign-language-mnist/sign_mnist_train/sign_mnist_train.csv"
    test_path = "/kaggle/input/sign-language-mnist/sign_mnist_test/sign_mnist_test.csv"
    epochs = 20
    main(train_path, test_path, epochs)